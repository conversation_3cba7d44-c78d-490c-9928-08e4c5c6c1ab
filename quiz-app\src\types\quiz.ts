export interface QuizOption {
  id: string;
  text: string;
  isCorrect: boolean;
}

export interface QuizQuestion {
  id: string;
  question: string;
  options: QuizOption[];
  explanation: string;
  difficulty: 'easy' | 'medium' | 'hard';
  topic: string;
  points: number;
}

export interface QuizAttempt {
  questionId: string;
  selectedOptionId: string;
  isCorrect: boolean;
  timeSpent: number;
}

export interface QuizResult {
  id: string;
  totalQuestions: number;
  correctAnswers: number;
  totalPoints: number;
  earnedPoints: number;
  accuracy: number;
  timeSpent: number;
  attempts: QuizAttempt[];
  completedAt: Date;
}

export interface QuizState {
  currentQuestionIndex: number;
  questions: QuizQuestion[];
  attempts: QuizAttempt[];
  startTime: number;
  isCompleted: boolean;
  showResults: boolean;
}
