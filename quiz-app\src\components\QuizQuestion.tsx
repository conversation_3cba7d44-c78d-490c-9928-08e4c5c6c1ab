import React from 'react';
import { motion } from 'framer-motion';
import { QuizQuestion as QuizQuestionType } from '../types/quiz';

interface QuizQuestionProps {
  question: QuizQuestionType;
  selectedOption: string | null;
  onOptionSelect: (optionId: string) => void;
  showFeedback: boolean;
  questionNumber: number;
  totalQuestions: number;
}

const QuizQuestion: React.FC<QuizQuestionProps> = ({
  question,
  selectedOption,
  onOptionSelect,
  showFeedback,
  questionNumber,
  totalQuestions
}) => {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'hard': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getOptionStyle = (option: any) => {
    if (!showFeedback) {
      return selectedOption === option.id 
        ? 'bg-blue-100 border-blue-500 text-blue-700' 
        : 'bg-white border-gray-300 hover:bg-gray-50';
    }
    
    if (option.isCorrect) {
      return 'bg-green-100 border-green-500 text-green-700';
    }
    
    if (selectedOption === option.id && !option.isCorrect) {
      return 'bg-red-100 border-red-500 text-red-700';
    }
    
    return 'bg-gray-100 border-gray-300 text-gray-600';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg"
    >
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <span className="text-sm font-medium text-gray-500">
            Question {questionNumber} of {totalQuestions}
          </span>
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getDifficultyColor(question.difficulty)}`}>
            {question.difficulty.toUpperCase()}
          </span>
          <span className="px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-600">
            {question.topic}
          </span>
        </div>
        <div className="text-sm font-medium text-gray-600">
          {question.points} points
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-gray-200 rounded-full h-2 mb-6">
        <div 
          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
          style={{ width: `${(questionNumber / totalQuestions) * 100}%` }}
        />
      </div>

      {/* Question */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-800 leading-relaxed">
          {question.question}
        </h2>
      </div>

      {/* Options */}
      <div className="space-y-3 mb-6">
        {question.options.map((option, index) => (
          <motion.button
            key={option.id}
            whileHover={{ scale: showFeedback ? 1 : 1.02 }}
            whileTap={{ scale: showFeedback ? 1 : 0.98 }}
            onClick={() => !showFeedback && onOptionSelect(option.id)}
            disabled={showFeedback}
            className={`w-full p-4 text-left border-2 rounded-lg transition-all duration-200 ${getOptionStyle(option)}`}
          >
            <div className="flex items-center">
              <span className="flex-shrink-0 w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-sm font-medium mr-4">
                {String.fromCharCode(65 + index)}
              </span>
              <span className="text-base">{option.text}</span>
              {showFeedback && option.isCorrect && (
                <span className="ml-auto text-green-600">✓</span>
              )}
              {showFeedback && selectedOption === option.id && !option.isCorrect && (
                <span className="ml-auto text-red-600">✗</span>
              )}
            </div>
          </motion.button>
        ))}
      </div>

      {/* Explanation */}
      {showFeedback && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.3 }}
          className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg"
        >
          <h3 className="font-semibold text-blue-800 mb-2">Explanation:</h3>
          <p className="text-blue-700">{question.explanation}</p>
        </motion.div>
      )}
    </motion.div>
  );
};

export default QuizQuestion;
