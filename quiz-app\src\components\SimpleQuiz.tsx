import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { mathQuestions } from '../data/questions';
import { QuizResult } from '../types/quiz';

const SimpleQuiz: React.FC = () => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [showFeedback, setShowFeedback] = useState(false);
  const [score, setScore] = useState(0);
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [startTime] = useState(Date.now());
  const [showAnimation, setShowAnimation] = useState(false);

  const currentQuestion = mathQuestions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === mathQuestions.length - 1;

  const handleOptionSelect = (optionId: string) => {
    if (showFeedback) return;
    setSelectedOption(optionId);
  };

  const submitAnswer = () => {
    if (!selectedOption) return;
    
    const isCorrect = currentQuestion.options.find(opt => opt.id === selectedOption)?.isCorrect || false;
    
    if (isCorrect) {
      setScore(prev => prev + currentQuestion.points);
    }
    
    setShowAnimation(true);
    setTimeout(() => {
      setShowAnimation(false);
      setShowFeedback(true);
    }, 1500);
  };

  const nextQuestion = () => {
    if (isLastQuestion) {
      setQuizCompleted(true);
    } else {
      setCurrentQuestionIndex(prev => prev + 1);
      setSelectedOption(null);
      setShowFeedback(false);
    }
  };

  const resetQuiz = () => {
    setCurrentQuestionIndex(0);
    setSelectedOption(null);
    setShowFeedback(false);
    setScore(0);
    setQuizCompleted(false);
  };

  const getOptionStyle = (option: any) => {
    let baseStyle = "option-button";
    
    if (!showFeedback) {
      return selectedOption === option.id ? `${baseStyle} selected` : baseStyle;
    }
    
    if (option.isCorrect) {
      return `${baseStyle} correct`;
    }
    
    if (selectedOption === option.id && !option.isCorrect) {
      return `${baseStyle} incorrect`;
    }
    
    return `${baseStyle} disabled`;
  };

  const calculateResults = (): QuizResult => {
    const totalPoints = mathQuestions.reduce((sum, q) => sum + q.points, 0);
    const accuracy = (score / totalPoints) * 100;
    
    return {
      id: Date.now().toString(),
      totalQuestions: mathQuestions.length,
      correctAnswers: Math.round((score / totalPoints) * mathQuestions.length),
      totalPoints,
      earnedPoints: score,
      accuracy,
      timeSpent: Date.now() - startTime,
      attempts: [],
      completedAt: new Date()
    };
  };

  if (quizCompleted) {
    const results = calculateResults();
    return (
      <div className="quiz-container">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="results-card"
        >
          <div className="results-header">
            <div className="trophy-icon">🏆</div>
            <h1>Quiz Complete!</h1>
            <div className="performance-badge">
              {results.accuracy >= 80 ? 'Excellent!' : results.accuracy >= 60 ? 'Good Job!' : 'Keep Practicing!'}
            </div>
          </div>
          
          <div className="results-stats">
            <div className="stat-card">
              <div className="stat-value">{results.earnedPoints}</div>
              <div className="stat-label">Points Earned</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{Math.round(results.accuracy)}%</div>
              <div className="stat-label">Accuracy</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{Math.round(results.timeSpent / 1000)}s</div>
              <div className="stat-label">Time Taken</div>
            </div>
          </div>
          
          <button onClick={resetQuiz} className="btn btn-primary">
            Take Quiz Again
          </button>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="quiz-container">
      {showAnimation && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="feedback-overlay"
        >
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            className={`feedback-animation ${
              currentQuestion.options.find(opt => opt.id === selectedOption)?.isCorrect 
                ? 'correct' : 'incorrect'
            }`}
          >
            <div className="feedback-icon">
              {currentQuestion.options.find(opt => opt.id === selectedOption)?.isCorrect ? '✅' : '❌'}
            </div>
            <div className="feedback-text">
              {currentQuestion.options.find(opt => opt.id === selectedOption)?.isCorrect 
                ? 'Correct!' : 'Not quite!'}
            </div>
          </motion.div>
        </motion.div>
      )}

      <motion.div
        key={currentQuestionIndex}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        className="question-card"
      >
        <div className="question-header">
          <div className="question-info">
            <span className="question-number">
              Question {currentQuestionIndex + 1} of {mathQuestions.length}
            </span>
            <span className={`difficulty-badge ${currentQuestion.difficulty}`}>
              {currentQuestion.difficulty.toUpperCase()}
            </span>
            <span className="topic-badge">{currentQuestion.topic}</span>
          </div>
          <div className="points-badge">{currentQuestion.points} points</div>
        </div>

        <div className="progress-bar">
          <div 
            className="progress-fill"
            style={{ width: `${((currentQuestionIndex + 1) / mathQuestions.length) * 100}%` }}
          />
        </div>

        <div className="question-text">
          <h2>{currentQuestion.question}</h2>
        </div>

        <div className="options-container">
          {currentQuestion.options.map((option, index) => (
            <motion.button
              key={option.id}
              whileHover={{ scale: showFeedback ? 1 : 1.02 }}
              whileTap={{ scale: showFeedback ? 1 : 0.98 }}
              onClick={() => handleOptionSelect(option.id)}
              disabled={showFeedback}
              className={getOptionStyle(option)}
            >
              <span className="option-letter">
                {String.fromCharCode(65 + index)}
              </span>
              <span className="option-text">{option.text}</span>
              {showFeedback && option.isCorrect && <span className="check-mark">✓</span>}
              {showFeedback && selectedOption === option.id && !option.isCorrect && <span className="x-mark">✗</span>}
            </motion.button>
          ))}
        </div>

        {showFeedback && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="explanation-box"
          >
            <h3>Explanation:</h3>
            <p>{currentQuestion.explanation}</p>
          </motion.div>
        )}

        <div className="action-buttons">
          <button onClick={resetQuiz} className="btn btn-secondary">
            Restart Quiz
          </button>
          
          <div className="right-buttons">
            {!showFeedback && selectedOption && (
              <motion.button
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                onClick={submitAnswer}
                className="btn btn-primary"
              >
                Submit Answer
              </motion.button>
            )}

            {showFeedback && (
              <motion.button
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                onClick={nextQuestion}
                className="btn btn-success"
              >
                {isLastQuestion ? 'View Results' : 'Next Question'}
              </motion.button>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default SimpleQuiz;
